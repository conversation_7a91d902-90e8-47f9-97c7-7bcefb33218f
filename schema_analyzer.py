"""
Database schema analysis and query validation utilities.
Provides intelligent schema understanding and query optimization suggestions.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from database_config import get_database_config
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_groq import ChatGroq
import json
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TableAnalysis:
    """Data class for table analysis results."""
    table_name: str
    row_count: int
    column_count: int
    primary_keys: List[str]
    foreign_keys: List[Dict[str, Any]]
    indexes: List[str]
    data_types: Dict[str, str]
    nullable_columns: List[str]
    business_context: str

@dataclass
class QueryOptimization:
    """Data class for query optimization suggestions."""
    original_query: str
    optimized_query: str
    performance_score: int
    suggestions: List[str]
    estimated_cost: str

class AnalizadorEsquema:
    """Database schema analyzer with AI-powered insights."""
    
    def __init__(self):
        """Initialize schema analyzer."""
        self.db_config = get_database_config()
        self.llm = ChatGroq(temperature=0, model="llama3-70b-8192")
        
        # Schema analysis prompt
        self.schema_analysis_prompt = PromptTemplate(
            input_variables=["table_info", "sample_data"],
            template="""
            You are a database expert analyzing a table schema. Based on the following information:
            
            Table Information: {table_info}
            Sample Data: {sample_data}
            
            Provide analysis in JSON format:
            {{
                "business_context": "What this table likely represents in business terms",
                "data_quality_issues": ["List potential data quality issues"],
                "optimization_opportunities": ["List optimization suggestions"],
                "relationships": ["Describe likely relationships with other tables"],
                "usage_patterns": ["Suggest common query patterns for this table"]
            }}
            """
        )
        
        # Query optimization prompt
        self.query_optimization_prompt = PromptTemplate(
            input_variables=["query", "schema_context", "performance_stats"],
            template="""
            You are a SQL performance expert. Analyze this query for optimization:
            
            Query: {query}
            Schema Context: {schema_context}
            Performance Stats: {performance_stats}
            
            Provide optimization analysis in JSON format:
            {{
                "performance_score": "Score from 1-10 (10 being optimal)",
                "bottlenecks": ["List performance bottlenecks"],
                "optimized_query": "Improved version of the query",
                "suggestions": ["Specific optimization suggestions"],
                "index_recommendations": ["Recommended indexes"],
                "estimated_improvement": "Expected performance improvement percentage"
            }}
            """
        )
        
        self.schema_chain = self.schema_analysis_prompt | self.llm | StrOutputParser()
        self.optimization_chain = self.query_optimization_prompt | self.llm | StrOutputParser()
    
    def analizar_tabla(self, table_name: str) -> TableAnalysis:
        """Analyze a specific table comprehensively."""
        try:
            logger.info(f"Analyzing table: {table_name}")
            
            # Get basic schema information
            schema_info = self.db_config.get_table_schema(table_name)
            if not schema_info:
                raise ValueError(f"Table {table_name} not found")
            
            # Get sample data
            sample_data = self._get_sample_data(table_name)
            
            # Get row count
            row_count = self._get_row_count(table_name)
            
            # Extract schema details
            columns = schema_info.get('columns', [])
            primary_keys = schema_info.get('primary_keys', {}).get('constrained_columns', [])
            foreign_keys = schema_info.get('foreign_keys', [])
            indexes = [idx['name'] for idx in schema_info.get('indexes', [])]
            
            # Build data type mapping
            data_types = {col['name']: col['type'].__class__.__name__ for col in columns}
            nullable_columns = [col['name'] for col in columns if col.get('nullable', True)]
            
            # Get AI analysis
            table_info_str = json.dumps({
                'columns': [{'name': col['name'], 'type': str(col['type']), 'nullable': col.get('nullable')} for col in columns],
                'primary_keys': primary_keys,
                'foreign_keys': foreign_keys,
                'row_count': row_count
            }, indent=2)
            
            ai_analysis = self.schema_chain.invoke({
                "table_info": table_info_str,
                "sample_data": json.dumps(sample_data[:5], indent=2) if sample_data else "No sample data available"
            })
            
            # Parse AI analysis
            try:
                business_context = json.loads(ai_analysis).get('business_context', 'Analysis unavailable')
            except json.JSONDecodeError:
                business_context = ai_analysis[:200] + "..." if len(ai_analysis) > 200 else ai_analysis
            
            return TableAnalysis(
                table_name=table_name,
                row_count=row_count,
                column_count=len(columns),
                primary_keys=primary_keys,
                foreign_keys=foreign_keys,
                indexes=indexes,
                data_types=data_types,
                nullable_columns=nullable_columns,
                business_context=business_context
            )
            
        except Exception as e:
            logger.error(f"Error analyzing table {table_name}: {e}")
            raise
    
    def optimizar_consulta(self, query: str) -> QueryOptimization:
        """Analyze and optimize a SQL query."""
        try:
            logger.info(f"Optimizing query: {query[:100]}...")
            
            # Get schema context
            schema_context = self._get_relevant_schema_context(query)
            
            # Get performance statistics (mock for now)
            performance_stats = self._get_query_performance_stats(query)
            
            # Get AI optimization analysis
            optimization_result = self.optimization_chain.invoke({
                "query": query,
                "schema_context": schema_context,
                "performance_stats": performance_stats
            })
            
            # Parse optimization result
            try:
                parsed_result = json.loads(optimization_result)
                return QueryOptimization(
                    original_query=query,
                    optimized_query=parsed_result.get('optimized_query', query),
                    performance_score=int(parsed_result.get('performance_score', 5)),
                    suggestions=parsed_result.get('suggestions', []),
                    estimated_cost=parsed_result.get('estimated_improvement', 'Unknown')
                )
            except (json.JSONDecodeError, ValueError):
                return QueryOptimization(
                    original_query=query,
                    optimized_query=query,
                    performance_score=5,
                    suggestions=[optimization_result[:200] + "..." if len(optimization_result) > 200 else optimization_result],
                    estimated_cost="Analysis unavailable"
                )
                
        except Exception as e:
            logger.error(f"Error optimizing query: {e}")
            raise
    
    def generar_reporte_esquema(self) -> Dict[str, Any]:
        """Generate comprehensive database schema report."""
        try:
            logger.info("Generating comprehensive schema report")
            
            tables = self.db_config.get_table_names()
            report = {
                "timestamp": datetime.now().isoformat(),
                "database_summary": {
                    "total_tables": len(tables),
                    "tables": tables
                },
                "table_analyses": {},
                "recommendations": []
            }
            
            # Analyze each table
            for table in tables[:10]:  # Limit to first 10 tables for performance
                try:
                    analysis = self.analizar_tabla(table)
                    report["table_analyses"][table] = {
                        "row_count": analysis.row_count,
                        "column_count": analysis.column_count,
                        "primary_keys": analysis.primary_keys,
                        "foreign_keys": len(analysis.foreign_keys),
                        "indexes": len(analysis.indexes),
                        "business_context": analysis.business_context
                    }
                except Exception as e:
                    logger.warning(f"Failed to analyze table {table}: {e}")
                    report["table_analyses"][table] = {"error": str(e)}
            
            # Generate general recommendations
            report["recommendations"] = self._generate_schema_recommendations(report["table_analyses"])
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating schema report: {e}")
            raise
    
    def _get_sample_data(self, table_name: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Get sample data from table."""
        try:
            query = f"SELECT * FROM {table_name} LIMIT {limit}"
            return self.db_config.execute_query(query)
        except Exception as e:
            logger.warning(f"Could not get sample data for {table_name}: {e}")
            return []
    
    def _get_row_count(self, table_name: str) -> int:
        """Get row count for table."""
        try:
            query = f"SELECT COUNT(*) as count FROM {table_name}"
            result = self.db_config.execute_query(query)
            return result[0]['count'] if result else 0
        except Exception as e:
            logger.warning(f"Could not get row count for {table_name}: {e}")
            return 0
    
    def _get_relevant_schema_context(self, query: str) -> str:
        """Extract relevant schema context for query optimization."""
        # Simple table extraction from query
        import re
        tables = re.findall(r'FROM\s+(\w+)|JOIN\s+(\w+)', query, re.IGNORECASE)
        table_names = [t[0] or t[1] for t in tables]
        
        context = []
        for table in table_names:
            try:
                schema = self.db_config.get_table_schema(table)
                if schema:
                    columns = [col['name'] for col in schema.get('columns', [])]
                    context.append(f"{table}: {', '.join(columns[:10])}")
            except Exception:
                continue
        
        return "; ".join(context)
    
    def _get_query_performance_stats(self, query: str) -> str:
        """Get mock performance statistics for query."""
        # In a real implementation, this would use EXPLAIN PLAN
        return "Mock performance stats: estimated_rows=1000, estimated_cost=10.5"
    
    def _generate_schema_recommendations(self, table_analyses: Dict[str, Any]) -> List[str]:
        """Generate general schema recommendations."""
        recommendations = []
        
        # Check for tables without primary keys
        no_pk_tables = [name for name, analysis in table_analyses.items() 
                       if isinstance(analysis, dict) and not analysis.get('primary_keys')]
        if no_pk_tables:
            recommendations.append(f"Consider adding primary keys to tables: {', '.join(no_pk_tables)}")
        
        # Check for large tables without indexes
        large_tables = [name for name, analysis in table_analyses.items()
                       if isinstance(analysis, dict) and analysis.get('row_count', 0) > 10000 and analysis.get('indexes', 0) < 2]
        if large_tables:
            recommendations.append(f"Consider adding indexes to large tables: {', '.join(large_tables)}")
        
        return recommendations
