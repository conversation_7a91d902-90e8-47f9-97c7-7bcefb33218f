"""
Enhanced Pandas Agent with advanced analytics, visualization, and statistical analysis capabilities.
Extends the existing financial data analysis with comprehensive data manipulation features.
"""

import logging
from typing import Dict, Any, List, Optional, Union
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import json
from langchain_experimental.agents.agent_toolkits import create_pandas_dataframe_agent
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_groq import ChatGroq
from langchain_core.tools import tool
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgenteAnalizarDatosAvanzado:
    """Enhanced Pandas agent with advanced analytics and visualization capabilities."""
    
    def __init__(self):
        """Initialize enhanced data analysis agent."""
        self.llm = ChatGroq(temperature=0, model="llama3-70b-8192")
        
        # Advanced analysis prompt template
        self.analysis_prompt = PromptTemplate(
            input_variables=["data_summary", "analysis_type", "specific_request"],
            template="""
            You are an expert data scientist and financial analyst. Based on the following data:
            
            Data Summary: {data_summary}
            Analysis Type: {analysis_type}
            Specific Request: {specific_request}
            
            Provide comprehensive analysis including:
            1. Statistical insights and patterns
            2. Trend analysis and forecasting
            3. Anomaly detection
            4. Correlation analysis
            5. Risk assessment
            6. Actionable recommendations
            
            Use advanced pandas operations and statistical methods. Current date: {current_date}
            """
        )
        
        # Visualization prompt template
        self.viz_prompt = PromptTemplate(
            input_variables=["data_info", "viz_type", "requirements"],
            template="""
            You are a data visualization expert. Create appropriate visualizations for:
            
            Data Information: {data_info}
            Visualization Type: {viz_type}
            Requirements: {requirements}
            
            Generate Python code using plotly to create interactive, professional visualizations.
            Include proper titles, labels, colors, and formatting.
            """
        )
        
        self.analysis_chain = self.analysis_prompt | self.llm | StrOutputParser()
        self.viz_chain = self.viz_prompt | self.llm | StrOutputParser()
    
    def ejecutar_analisis_avanzado(self, datos: pd.DataFrame, consulta: str, tipo_analisis: str = "comprehensive") -> Dict[str, Any]:
        """Execute advanced data analysis with multiple analytical approaches."""
        try:
            logger.info(f"Executing advanced analysis: {tipo_analisis}")
            
            # Prepare data summary
            data_summary = self._generate_data_summary(datos)
            
            # Create enhanced pandas agent
            agente_pandas = create_pandas_dataframe_agent(
                llm=self.llm,
                df=datos,
                verbose=True,
                allow_dangerous_code=True,
                agent_executor_kwargs={
                    "handle_parsing_errors": True,
                    "max_iterations": 10
                }
            )
            
            # Enhanced system message for advanced analysis
            mensaje_sistema = f"""
            You are an expert financial data analyst with advanced statistical knowledge.
            
            Dataset Information: {data_summary}
            Analysis Type: {tipo_analisis}
            User Query: {consulta}
            Current Date: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
            
            Perform comprehensive analysis including:
            
            1. DESCRIPTIVE STATISTICS:
               - Calculate mean, median, mode, standard deviation, variance
               - Identify quartiles, percentiles, and outliers
               - Analyze distribution characteristics
            
            2. TREND ANALYSIS:
               - Calculate moving averages (7, 30, 200 periods)
               - Identify trend direction and strength
               - Detect trend reversals and breakouts
            
            3. VOLATILITY ANALYSIS:
               - Calculate rolling volatility
               - Identify high/low volatility periods
               - Risk metrics (VaR, Sharpe ratio if applicable)
            
            4. CORRELATION ANALYSIS:
               - Analyze relationships between variables
               - Identify leading/lagging indicators
            
            5. ANOMALY DETECTION:
               - Identify unusual patterns or outliers
               - Flag potential data quality issues
            
            6. FORECASTING INSIGHTS:
               - Short-term trend predictions
               - Seasonal pattern analysis
            
            Use advanced pandas operations, numpy statistical functions, and provide actionable insights.
            If you encounter errors, try alternative approaches and continue with available analysis.
            """
            
            # Execute comprehensive analysis
            resultado = agente_pandas.invoke({
                "input": [consulta, mensaje_sistema]
            })
            
            # Generate additional statistical insights
            insights_estadisticos = self._generar_insights_estadisticos(datos)
            
            # Create visualizations
            visualizaciones = self._crear_visualizaciones_avanzadas(datos, tipo_analisis)
            
            return {
                "success": True,
                "analisis_principal": resultado["output"],
                "insights_estadisticos": insights_estadisticos,
                "visualizaciones": visualizaciones,
                "resumen_datos": data_summary,
                "tipo_analisis": tipo_analisis,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in advanced analysis: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def _generate_data_summary(self, datos: pd.DataFrame) -> Dict[str, Any]:
        """Generate comprehensive data summary."""
        try:
            summary = {
                "shape": datos.shape,
                "columns": list(datos.columns),
                "dtypes": datos.dtypes.to_dict(),
                "missing_values": datos.isnull().sum().to_dict(),
                "date_range": None,
                "numeric_columns": list(datos.select_dtypes(include=[np.number]).columns),
                "categorical_columns": list(datos.select_dtypes(include=['object']).columns)
            }
            
            # Add date range if index is datetime
            if isinstance(datos.index, pd.DatetimeIndex):
                summary["date_range"] = {
                    "start": datos.index.min().strftime("%Y-%m-%d"),
                    "end": datos.index.max().strftime("%Y-%m-%d"),
                    "periods": len(datos)
                }
            
            # Add basic statistics for numeric columns
            if summary["numeric_columns"]:
                summary["basic_stats"] = datos[summary["numeric_columns"]].describe().to_dict()
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating data summary: {e}")
            return {"error": str(e)}
    
    def _generar_insights_estadisticos(self, datos: pd.DataFrame) -> Dict[str, Any]:
        """Generate advanced statistical insights."""
        try:
            insights = {}
            numeric_cols = datos.select_dtypes(include=[np.number]).columns
            
            if len(numeric_cols) > 0:
                # Correlation analysis
                if len(numeric_cols) > 1:
                    correlations = datos[numeric_cols].corr()
                    insights["correlaciones"] = correlations.to_dict()
                
                # Volatility analysis for time series data
                if isinstance(datos.index, pd.DatetimeIndex):
                    for col in numeric_cols:
                        if col.lower() in ['close', 'price', 'value']:
                            returns = datos[col].pct_change().dropna()
                            insights[f"volatilidad_{col}"] = {
                                "volatilidad_diaria": returns.std(),
                                "volatilidad_anualizada": returns.std() * np.sqrt(252),
                                "max_drawdown": self._calculate_max_drawdown(datos[col])
                            }
                
                # Outlier detection using IQR method
                for col in numeric_cols:
                    Q1 = datos[col].quantile(0.25)
                    Q3 = datos[col].quantile(0.75)
                    IQR = Q3 - Q1
                    outliers = datos[(datos[col] < Q1 - 1.5 * IQR) | (datos[col] > Q3 + 1.5 * IQR)]
                    insights[f"outliers_{col}"] = len(outliers)
            
            return insights
            
        except Exception as e:
            logger.error(f"Error generating statistical insights: {e}")
            return {"error": str(e)}
    
    def _calculate_max_drawdown(self, series: pd.Series) -> float:
        """Calculate maximum drawdown for a price series."""
        try:
            cumulative = (1 + series.pct_change()).cumprod()
            running_max = cumulative.expanding().max()
            drawdown = (cumulative - running_max) / running_max
            return drawdown.min()
        except Exception:
            return 0.0
    
    def _crear_visualizaciones_avanzadas(self, datos: pd.DataFrame, tipo_analisis: str) -> Dict[str, Any]:
        """Create advanced interactive visualizations."""
        try:
            visualizaciones = {}
            numeric_cols = datos.select_dtypes(include=[np.number]).columns
            
            if len(numeric_cols) == 0:
                return {"error": "No numeric columns found for visualization"}
            
            # Time series plot if data has datetime index
            if isinstance(datos.index, pd.DatetimeIndex) and len(numeric_cols) > 0:
                fig_ts = go.Figure()
                
                for col in numeric_cols[:3]:  # Limit to first 3 numeric columns
                    fig_ts.add_trace(go.Scatter(
                        x=datos.index,
                        y=datos[col],
                        mode='lines',
                        name=col,
                        line=dict(width=2)
                    ))
                
                fig_ts.update_layout(
                    title="Time Series Analysis",
                    xaxis_title="Date",
                    yaxis_title="Value",
                    hovermode='x unified',
                    template='plotly_white'
                )
                
                visualizaciones["time_series"] = fig_ts.to_json()
            
            # Correlation heatmap
            if len(numeric_cols) > 1:
                corr_matrix = datos[numeric_cols].corr()
                
                fig_corr = go.Figure(data=go.Heatmap(
                    z=corr_matrix.values,
                    x=corr_matrix.columns,
                    y=corr_matrix.columns,
                    colorscale='RdBu',
                    zmid=0,
                    text=np.round(corr_matrix.values, 2),
                    texttemplate="%{text}",
                    textfont={"size": 10}
                ))
                
                fig_corr.update_layout(
                    title="Correlation Matrix",
                    template='plotly_white'
                )
                
                visualizaciones["correlation_heatmap"] = fig_corr.to_json()
            
            # Distribution plots
            if len(numeric_cols) > 0:
                fig_dist = make_subplots(
                    rows=min(2, len(numeric_cols)),
                    cols=min(2, (len(numeric_cols) + 1) // 2),
                    subplot_titles=list(numeric_cols[:4])
                )
                
                for i, col in enumerate(numeric_cols[:4]):
                    row = i // 2 + 1
                    col_pos = i % 2 + 1
                    
                    fig_dist.add_trace(
                        go.Histogram(x=datos[col], name=col, showlegend=False),
                        row=row, col=col_pos
                    )
                
                fig_dist.update_layout(
                    title="Distribution Analysis",
                    template='plotly_white'
                )
                
                visualizaciones["distributions"] = fig_dist.to_json()
            
            return visualizaciones
            
        except Exception as e:
            logger.error(f"Error creating visualizations: {e}")
            return {"error": str(e)}

@tool
def AnalisisAvanzadoDatos(datos_json: str, consulta: str, tipo_analisis: str = "comprehensive") -> Dict[str, Any]:
    """
    Perform advanced data analysis with statistical insights and visualizations.
    
    Args:
        datos_json (str): JSON representation of pandas DataFrame
        consulta (str): Analysis query or request
        tipo_analisis (str): Type of analysis (comprehensive, statistical, trend, etc.)
        
    Returns:
        Dict[str, Any]: Advanced analysis results with insights and visualizations
    """
    try:
        # Convert JSON back to DataFrame
        datos = pd.read_json(datos_json)
        
        agente = AgenteAnalizarDatosAvanzado()
        return agente.ejecutar_analisis_avanzado(datos, consulta, tipo_analisis)
        
    except Exception as e:
        logger.error(f"Error in advanced data analysis tool: {e}")
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
